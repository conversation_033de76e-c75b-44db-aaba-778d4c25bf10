"""Test SearXNG MCP server using in-memory transport."""

from collections.abc import AsyncGenerator
from typing import Any
from unittest.mock import patch

import pytest
from fastmcp import Client
from fastmcp.exceptions import ToolError
from mcp.shared.exceptions import McpError

from app.main import mcp


class TestSearXNGMCPServer:
    """Test the SearXNG MCP server functionality."""

    @pytest.fixture
    async def client(self) -> AsyncGenerator[Client[Any], None]:
        """Create a test client using in-memory transport."""
        async with Client(mcp) as client:
            yield client

    async def test_server_connection(self, client: Client[Any]) -> None:
        """Test server connection."""
        # Test ping
        await client.ping()
        # If we get here without exception, the connection works

    async def test_list_tools(self, client: Client[Any]) -> None:
        """Test listing available tools."""
        tools = await client.list_tools()

        # Check that we have the expected SearXNG tools
        tool_names = [tool.name for tool in tools]
        expected_tools = [
            "search",
            "test_connection",
            "search_with_filters",
            "quick_search",
        ]

        for expected_tool in expected_tools:
            assert expected_tool in tool_names

    @patch("app.tools.search_searxng")
    async def test_search_tool(self, mock_search: Any, client: Client[Any]) -> None:
        """Test the main search tool."""
        # Mock the search response
        from app.search import SearchResult, SearXNGResponse

        mock_response = SearXNGResponse(
            query="test query",
            number_of_results=1,
            results=[
                SearchResult(
                    url="https://example.com",
                    title="Test Result",
                    content="This is a test result",
                )
            ],
        )
        mock_search.return_value = mock_response

        result = await client.call_tool("search", {"query": "test query"})

        assert "test query" in result.data
        assert "Test Result" in result.data
        assert "https://example.com" in result.data
        mock_search.assert_called_once()

    @patch("app.tools.search_searxng")
    async def test_quick_search_tool(
        self, mock_search: Any, client: Client[Any]
    ) -> None:
        """Test the quick search tool."""
        from app.search import SearchResult, SearXNGResponse

        mock_response = SearXNGResponse(
            query="quick test",
            number_of_results=1,
            results=[
                SearchResult(
                    url="https://example.com",
                    title="Quick Result",
                    content="Quick search result",
                )
            ],
        )
        mock_search.return_value = mock_response

        result = await client.call_tool("quick_search", {"query": "quick test"})

        assert "quick test" in result.data
        assert "Quick Result" in result.data
        mock_search.assert_called_once()

    @patch("app.tools.test_searxng_connection")
    async def test_connection_tool(self, mock_test: Any, client: Client[Any]) -> None:
        """Test the connection test tool."""
        mock_test.return_value = True

        result = await client.call_tool("test_connection", {})

        assert "Successfully connected" in result.data
        mock_test.assert_called_once()

    async def test_list_resources(self, client: Client[Any]) -> None:
        """Test listing available resources."""
        resources = await client.list_resources()

        # Check that we have the expected SearXNG resource templates
        resource_uris = [str(resource.uri) for resource in resources]
        expected_resources = ["config://searxng", "status://searxng"]

        for expected_resource in expected_resources:
            assert expected_resource in resource_uris

    async def test_read_searxng_config_resource(self, client: Client[Any]) -> None:
        """Test reading the SearXNG config resource."""
        resource = await client.read_resource("config://searxng")

        # Parse the JSON content
        import json

        resource_content = resource[0]
        if hasattr(resource_content, "text"):
            config_data = json.loads(resource_content.text)
        else:
            raise ValueError("Expected text resource content")

        assert config_data["server_name"] == "SearXNG MCP Server"
        assert config_data["server_version"] == "0.1.0"
        assert "searxng_url" in config_data
        assert "search_limit" in config_data
        assert "search_timeout" in config_data

    async def test_read_searxng_status_resource(self, client: Client[Any]) -> None:
        """Test reading the SearXNG status resource."""
        resource = await client.read_resource("status://searxng")

        import json

        resource_content = resource[0]
        if hasattr(resource_content, "text"):
            status_data = json.loads(resource_content.text)
        else:
            raise ValueError("Expected text resource content")

        assert status_data["server_status"] == "running"
        assert "searxng_url" in status_data
        assert "timestamp" in status_data
        assert status_data["version"] == "0.1.0"

    async def test_list_prompts(self, client: Client[Any]) -> None:
        """Test listing available prompts."""
        prompts = await client.list_prompts()

        # Check that we have the expected SearXNG prompts
        prompt_names = [prompt.name for prompt in prompts]
        expected_prompts = [
            "search_prompt",
            "research_prompt",
            "news_search_prompt",
            "fact_check_prompt",
            "comparison_search_prompt",
            "academic_search_prompt",
        ]

        for expected_prompt in expected_prompts:
            assert expected_prompt in prompt_names

    async def test_get_prompt(self, client: Client[Any]) -> None:
        """Test getting prompt content."""
        # Test search_prompt
        prompt = await client.get_prompt(
            "search_prompt",
            {"query": "artificial intelligence", "context": "research project"},
        )
        prompt_str = str(prompt)
        assert "artificial intelligence" in prompt_str
        assert "research project" in prompt_str
        assert "SearXNG" in prompt_str

        # Test research_prompt
        research_prompt = await client.get_prompt(
            "research_prompt", {"topic": "climate change", "depth": "comprehensive"}
        )
        research_prompt_str = str(research_prompt)
        assert "climate change" in research_prompt_str
        assert "comprehensive" in research_prompt_str

    async def test_error_handling(self, client: Client[Any]) -> None:
        """Test error handling for invalid requests."""
        # Test calling non-existent tool
        with pytest.raises(ToolError):
            await client.call_tool("non_existent_tool", {})

        # Test reading non-existent resource
        with pytest.raises(McpError):
            await client.read_resource("invalid://resource")
