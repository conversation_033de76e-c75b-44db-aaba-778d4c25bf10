"""Pytest configuration for FastMCP tests."""

import os
from collections.abc import Generator
from typing import Any

import pytest


@pytest.fixture(autouse=True)
def setup_test_environment() -> Generator[None, None, None]:
    """Set up test environment variables to suppress warnings."""
    # Set debug mode to suppress production warnings
    os.environ["MCP_DEBUG"] = "true"
    os.environ["MCP_LOG_LEVEL"] = "warning"  # Reduce log noise in tests

    yield

    # Clean up after tests
    os.environ.pop("MCP_DEBUG", None)
    os.environ.pop("MCP_LOG_LEVEL", None)


@pytest.fixture
def test_settings() -> Any:
    """Provide test-specific settings."""
    from app.config import Settings

    return Settings(
        debug=True,
        log_level="warning",
        auth_secret="test-secret-key-for-testing-purposes-only-32-chars",
        transport="stdio",  # Use stdio for testing
    )
