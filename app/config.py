"""FastMCP server configuration using Pydantic Settings."""

from typing import Literal

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """FastMCP server settings."""

    # Server Information
    server_name: str = Field(
        default="SearXNG MCP Server", description="MCP server name"
    )
    server_version: str = Field(default="0.1.0", description="MCP server version")
    debug: bool = Field(default=False, description="Debug mode")

    # Transport Configuration
    transport: Literal["stdio", "http", "sse"] = Field(
        default="stdio", description="MCP transport protocol"
    )
    host: str = Field(
        default="127.0.0.1", description="Server host (for HTTP/SSE transport)"
    )
    port: int = Field(
        default=8000, description="Server port (for HTTP/SSE transport)", ge=1, le=65535
    )
    path: str = Field(
        default="/mcp", description="MCP endpoint path (for HTTP transport)"
    )

    # Development
    reload: bool = Field(default=False, description="Auto-reload on code changes")

    # Logging
    log_level: Literal["debug", "info", "warning", "error", "critical"] = Field(
        default="info", description="Logging level"
    )

    # Authentication (for HTTP/SSE transports)
    auth_enabled: bool = Field(default=False, description="Enable authentication")
    auth_secret: str = Field(
        default="dev-secret-key-for-development-only-change-in-production",
        description="Secret key for authentication",
        min_length=32,
    )

    # SearXNG Configuration
    searxng_url: str = Field(
        default="http://localhost:8080",
        description="SearXNG instance URL",
        alias="SEARXNG_URL",
    )
    search_limit: int = Field(
        default=10,
        description="Maximum number of search results to return",
        ge=1,
        le=50,
    )
    search_timeout: int = Field(
        default=30, description="Search request timeout in seconds", ge=5, le=120
    )

    @field_validator("auth_secret")
    @classmethod
    def validate_auth_secret(cls, v: str) -> str:
        """Validate auth secret strength."""
        import os

        # Check if we're in a development/test environment
        is_development_or_test = (
            os.getenv("MCP_DEBUG", "false").lower() == "true"
            or os.getenv("PYTEST_CURRENT_TEST") is not None
            or os.getenv("TESTING") is not None
            or "pytest" in os.getenv("_", "")
            or "test" in os.getenv("PYTHONPATH", "").lower()
        )

        # Check if we're using localhost (development)
        host = os.getenv("MCP_HOST", "127.0.0.1")
        is_localhost = host in ["127.0.0.1", "localhost", "0.0.0.0"]

        # Only warn if using default secret in production-like environments
        # (network transport + not localhost + not development/test)
        if (
            v == "dev-secret-key-for-development-only-change-in-production"
            and not is_development_or_test
            and not is_localhost
            and os.getenv("MCP_TRANSPORT", "stdio") in ["http", "sse"]
        ):
            import warnings

            warnings.warn(
                "Using default auth secret! Change this in production!",
                UserWarning,
                stacklevel=2,
            )
        return v

    @property
    def is_http_transport(self) -> bool:
        """Check if using HTTP-based transport."""
        return self.transport in ("http", "sse")

    # Pydantic v2 configuration
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",  # Ignore extra fields instead of forbidding them
        validate_default=True,
        env_prefix="MCP_",  # Environment variable prefix for MCP settings
    )


# Global settings instance
settings = Settings()
