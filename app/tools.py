"""SearXNG MCP tools - Functions that LLMs can call to perform search actions."""

import logging
from typing import Any

from fastmcp import Context

from .search import search_searxng, test_searxng_connection

logger = logging.getLogger(__name__)


def register_tools(mcp: Any) -> None:
    """Register all SearXNG tools with the FastMCP server instance."""

    @mcp.tool
    async def search(
        query: str,
        limit: int | None = None,
        categories: str | None = None,
        engines: str | None = None,
        language: str | None = None,
        time_range: str | None = None,
        safesearch: int | None = None,
        include_infoboxes: bool = True,
        ctx: Context | None = None,
    ) -> dict:
        """Search the web using SearXNG metasearch engine.

        This tool aggregates results from multiple search engines including Google, Bing,
        DuckDuckGo, Brave, and many others through a SearXNG instance.

        Args:
            query: Search query string
            limit: Maximum number of results to return (default from settings)
            categories: Comma-separated list of categories (e.g., "general,news,images")
            engines: Comma-separated list of specific engines to use
            language: Language code for search (e.g., "en", "es", "fr")
            time_range: Time range filter ("day", "month", "year")
            safesearch: Safe search level (0=off, 1=moderate, 2=strict)
            include_infoboxes: Whether to include infobox results in output
            ctx: MCP context (automatically injected)

        Returns:
            Structured search results as dictionary
        """
        if ctx:
            await ctx.info(f"Searching for: {query}")

        try:
            # Perform the search
            response = await search_searxng(
                query=query,
                limit=limit,
                categories=categories,
                engines=engines,
                language=language,
                time_range=time_range,
                safesearch=safesearch,
            )

            # Filter infoboxes if not requested
            if not include_infoboxes:
                response.infoboxes = []

            if ctx:
                await ctx.info(f"Found {len(response.results)} results")

            # Return structured data as dictionary
            return response.model_dump()

        except Exception as e:
            error_msg = f"Search failed: {str(e)}"
            logger.error(error_msg)
            if ctx:
                await ctx.error(error_msg)
            return {"error": error_msg, "query": query, "number_of_results": 0, "results": [], "infoboxes": [], "suggestions": [], "answers": [], "corrections": [], "unresponsive_engines": []}

    @mcp.tool
    async def test_connection(ctx: Context | None = None) -> str:
        """Test connection to the SearXNG instance.

        Args:
            ctx: MCP context (automatically injected)

        Returns:
            Connection status message
        """
        if ctx:
            await ctx.info("Testing SearXNG connection...")

        try:
            is_connected = await test_searxng_connection()
            if is_connected:
                message = "✅ Successfully connected to SearXNG instance"
                if ctx:
                    await ctx.info("Connection test successful")
            else:
                message = "❌ Failed to connect to SearXNG instance"
                if ctx:
                    await ctx.warning("Connection test failed")

            return message

        except Exception as e:
            error_msg = f"❌ Connection test error: {str(e)}"
            logger.error(error_msg)
            if ctx:
                await ctx.error(error_msg)
            return error_msg

    @mcp.tool
    async def search_with_filters(
        query: str,
        category: str = "general",
        engine: str | None = None,
        safe_search: bool = True,
        ctx: Context | None = None,
    ) -> dict:
        """Search with predefined filters for common use cases.

        Args:
            query: Search query string
            category: Search category (general, news, images, videos, music, files, science)
            engine: Specific search engine to use (optional)
            safe_search: Enable safe search filtering
            ctx: MCP context (automatically injected)

        Returns:
            Structured search results as dictionary
        """
        if ctx:
            await ctx.info(f"Searching {category} for: {query}")

        # Map safe_search boolean to SearXNG safesearch levels
        safesearch_level = 1 if safe_search else 0

        try:
            response = await search_searxng(
                query=query,
                categories=category,
                engines=engine,
                safesearch=safesearch_level,
                limit=10,
            )

            if ctx:
                await ctx.info(f"Found {len(response.results)} results in {category}")

            # Return structured data as dictionary
            return response.model_dump()

        except Exception as e:
            error_msg = f"Filtered search failed: {str(e)}"
            logger.error(error_msg)
            if ctx:
                await ctx.error(error_msg)
            return {"error": error_msg, "query": query, "number_of_results": 0, "results": [], "infoboxes": [], "suggestions": [], "answers": [], "corrections": [], "unresponsive_engines": []}

    @mcp.tool
    async def quick_search(query: str, ctx: Context | None = None) -> dict:
        """Quick search with default settings for fast results.

        Args:
            query: Search query string
            ctx: MCP context (automatically injected)

        Returns:
            Structured search results as dictionary (limited to 5 results)
        """
        if ctx:
            await ctx.info(f"Quick search for: {query}")

        try:
            response = await search_searxng(
                query=query,
                limit=5,
                safesearch=1,  # Moderate safe search
            )

            if ctx:
                await ctx.info(
                    f"Quick search completed with {len(response.results)} results"
                )

            # Return structured data as dictionary
            return response.model_dump()

        except Exception as e:
            error_msg = f"Quick search failed: {str(e)}"
            logger.error(error_msg)
            if ctx:
                await ctx.error(error_msg)
            return {"error": error_msg, "query": query, "number_of_results": 0, "results": [], "infoboxes": [], "suggestions": [], "answers": [], "corrections": [], "unresponsive_engines": []}
