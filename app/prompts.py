"""SearXNG MCP prompts - Reusable message templates for search interactions."""

import logging
from typing import Any

logger = logging.getLogger(__name__)


def register_prompts(mcp: Any) -> None:
    """Register all SearXNG prompts with the FastMCP server instance."""

    @mcp.prompt
    def search_prompt(query: str, context: str = "") -> str:
        """Generate a prompt for web search using SearXNG.

        Args:
            query: Search query to execute
            context: Additional context for the search
        """
        base_prompt = f"Searching for '{query}' using SearXNG metasearch engine"

        if context:
            return f"{base_prompt}\n\nContext: {context}\n\nThis search will aggregate results from multiple search engines including Google, Bing, DuckDuckGo, Brave, and others to provide comprehensive results."

        return f"{base_prompt}\n\nThis search will aggregate results from multiple search engines including Google, Bing, DuckDuckGo, Brave, and others to provide comprehensive results."

    @mcp.prompt
    def research_prompt(topic: str, depth: str = "comprehensive") -> str:
        """Generate a prompt for research using SearXNG.

        Args:
            topic: Research topic
            depth: Depth of research (quick, comprehensive, academic)
        """
        depth_instructions = {
            "quick": "Provide a quick overview with 3-5 key search queries to get started",
            "comprehensive": "Conduct thorough research with multiple search strategies and diverse sources",
            "academic": "Focus on scholarly sources, research papers, and authoritative references",
        }

        instruction = depth_instructions.get(depth, depth_instructions["comprehensive"])

        return f"""Research the topic: {topic}

Research approach: {instruction}

Use SearXNG to search for information about this topic. Consider:
1. Multiple search queries with different keywords and phrases
2. Different categories (general, news, academic, images if relevant)
3. Various search engines for diverse perspectives
4. Time-sensitive information if applicable
5. Related topics and subtopics

Compile the findings into a comprehensive overview with proper source attribution."""

    @mcp.prompt
    def fact_check_prompt(claim: str, sources_required: int = 3) -> str:
        """Generate a prompt for fact-checking using SearXNG.

        Args:
            claim: Claim or statement to fact-check
            sources_required: Minimum number of sources to verify
        """
        return f"""Fact-check the following claim using SearXNG:

Claim: "{claim}"

Instructions:
1. Search for information about this claim using multiple search queries
2. Look for authoritative sources (news outlets, academic institutions, government agencies)
3. Find at least {sources_required} independent sources
4. Check for recent information and updates
5. Look for contradictory information or alternative perspectives
6. Verify the credibility of sources

Provide a summary of findings with:
- Verification status (True/False/Partially True/Unverified)
- Supporting evidence with source URLs
- Any contradictory information found
- Source credibility assessment
- Confidence level in the verification"""

    @mcp.prompt
    def news_search_prompt(topic: str, time_range: str = "recent") -> str:
        """Generate a prompt for searching news using SearXNG.

        Args:
            topic: News topic to search for
            time_range: Time range for news (recent, day, week, month)
        """
        time_instructions = {
            "recent": "Focus on the most recent news and developments",
            "day": "Search for news from the past day",
            "week": "Search for news from the past week",
            "month": "Search for news from the past month",
        }

        instruction = time_instructions.get(time_range, time_instructions["recent"])

        return f"""Search for news about: {topic}

Time focus: {instruction}

Use SearXNG to find current news and information:
1. Search news category specifically
2. Use multiple news-related search engines
3. Look for breaking news and recent developments
4. Find diverse perspectives from different news sources
5. Check for official statements or press releases
6. Verify information across multiple sources

Compile findings with publication dates and source credibility."""

    @mcp.prompt
    def comparison_search_prompt(items: str, criteria: str = "") -> str:
        """Generate a prompt for comparing items using SearXNG.

        Args:
            items: Items to compare (comma-separated)
            criteria: Specific criteria for comparison
        """
        criteria_text = f"\n\nComparison criteria: {criteria}" if criteria else ""

        return f"""Compare the following items using SearXNG: {items}{criteria_text}

Search strategy:
1. Search for each item individually to gather comprehensive information
2. Look for direct comparison articles or reviews
3. Search for pros and cons of each item
4. Find user reviews and expert opinions
5. Look for technical specifications or feature comparisons
6. Search for pricing and availability information
7. Find recent updates or changes to each item

Compile a comprehensive comparison covering:
- Key features and differences
- Advantages and disadvantages
- User feedback and ratings
- Expert recommendations
- Price comparisons
- Use case recommendations"""

    @mcp.prompt
    def academic_search_prompt(topic: str, level: str = "general") -> str:
        """Generate a prompt for academic research using SearXNG.

        Args:
            topic: Academic topic to research
            level: Academic level (undergraduate, graduate, research)
        """
        level_instructions = {
            "undergraduate": "Focus on introductory and foundational sources",
            "graduate": "Include advanced topics and specialized research",
            "research": "Prioritize peer-reviewed papers and cutting-edge research",
        }

        instruction = level_instructions.get(level, level_instructions["general"])

        return f"""Conduct academic research on: {topic}

Academic level: {instruction}

Research approach using SearXNG:
1. Search for academic papers and scholarly articles
2. Look for educational institution resources
3. Find textbooks and reference materials
4. Search for conference proceedings and presentations
5. Look for government and institutional reports
6. Find expert opinions and analysis
7. Search for recent developments and research trends

Compile findings with:
- Key concepts and definitions
- Major theories and frameworks
- Recent research and developments
- Authoritative sources and citations
- Different perspectives and debates
- Practical applications and implications"""
