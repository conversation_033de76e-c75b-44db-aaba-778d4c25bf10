"""SearXNG search functionality with Pydantic models and API client."""

import logging

import httpx
from pydantic import BaseModel, Field

from .config import settings

logger = logging.getLogger(__name__)


class SearchResult(BaseModel):
    """Individual search result from SearXNG."""

    url: str = Field(description="URL of the search result")
    title: str = Field(description="Title of the search result")
    content: str = Field(description="Content/snippet of the search result")
    engine: str | None = Field(
        default=None, description="Search engine that provided this result"
    )
    score: float | None = Field(default=None, description="Relevance score")
    category: str | None = Field(default=None, description="Category of the result")
    publishedDate: str | None = Field(
        default=None, description="Publication date if available"
    )


class InfoboxUrl(BaseModel):
    """URL within an infobox."""

    title: str = Field(description="Title of the URL")
    url: str = Field(description="The URL")


class Infobox(BaseModel):
    """Infobox result from SearXNG."""

    infobox: str = Field(description="Name/type of the infobox")
    id: str = Field(description="Unique identifier for the infobox")
    content: str = Field(description="Main content of the infobox")
    urls: list[InfoboxUrl] = Field(default_factory=list, description="Related URLs")
    img_src: str | None = Field(
        default=None, description="Image source URL if available"
    )
    engine: str | None = Field(
        default=None, description="Engine that provided the infobox"
    )


class SearXNGResponse(BaseModel):
    """Complete response from SearXNG API."""

    query: str = Field(description="The search query")
    number_of_results: int = Field(description="Total number of results found")
    results: list[SearchResult] = Field(
        default_factory=list, description="Search results"
    )
    infoboxes: list[Infobox] = Field(
        default_factory=list, description="Infobox results"
    )
    answers: list[str] = Field(default_factory=list, description="Direct answers")
    corrections: list[str] = Field(
        default_factory=list, description="Query corrections"
    )
    suggestions: list[str] = Field(
        default_factory=list, description="Search suggestions"
    )
    unresponsive_engines: list[str] = Field(
        default_factory=list, description="Engines that didn't respond"
    )


async def search_searxng(
    query: str,
    limit: int | None = None,
    categories: str | None = None,
    engines: str | None = None,
    language: str | None = None,
    time_range: str | None = None,
    safesearch: int | None = None,
) -> SearXNGResponse:
    """
    Search using SearXNG API.

    Args:
        query: Search query string
        limit: Maximum number of results to return (overrides settings default)
        categories: Comma-separated list of categories to search
        engines: Comma-separated list of engines to use
        language: Language code for search
        time_range: Time range filter (day, month, year)
        safesearch: Safe search level (0=off, 1=moderate, 2=strict)

    Returns:
        SearXNGResponse object with search results

    Raises:
        httpx.HTTPError: If the request fails
        ValueError: If the response cannot be parsed
    """
    if limit is None:
        limit = settings.search_limit

    # Build search parameters
    params: dict[str, str] = {
        "q": query,
        "format": "json",
    }

    if categories:
        params["categories"] = categories
    if engines:
        params["engines"] = engines
    if language:
        params["language"] = language
    if time_range:
        params["time_range"] = time_range
    if safesearch is not None:
        params["safesearch"] = str(safesearch)

    logger.info(f"Searching SearXNG for: {query}")
    logger.debug(f"Search parameters: {params}")

    async with httpx.AsyncClient(
        base_url=settings.searxng_url, timeout=settings.search_timeout
    ) as client:
        try:
            response = await client.get("/search", params=params)
            response.raise_for_status()

            # Parse the JSON response
            data = response.json()
            searxng_response = SearXNGResponse.model_validate(data)

            # Fix number_of_results if SearXNG didn't provide it correctly
            # This is a known issue where SearXNG often returns 0 even when there are results
            if searxng_response.number_of_results == 0 and searxng_response.results:
                searxng_response.number_of_results = len(searxng_response.results)

            # Limit results if specified
            if limit and len(searxng_response.results) > limit:
                searxng_response.results = searxng_response.results[:limit]
                # Update number_of_results to reflect the limited results
                searxng_response.number_of_results = len(searxng_response.results)

            logger.info(
                f"Found {len(searxng_response.results)} results for query: {query}"
            )
            return searxng_response

        except httpx.HTTPError as e:
            logger.error(f"HTTP error during search: {e}")
            raise
        except Exception as e:
            logger.error(f"Error parsing SearXNG response: {e}")
            raise ValueError(f"Failed to parse SearXNG response: {e}") from e


def format_search_results(
    response: SearXNGResponse, include_infoboxes: bool = True
) -> str:
    """
    Format search results into a readable text format.

    Args:
        response: SearXNG response object
        include_infoboxes: Whether to include infobox results

    Returns:
        Formatted string with search results
    """
    output = []

    # Add query information
    output.append(f"Search Query: {response.query}")
    output.append(f"Total Results: {response.number_of_results}")
    output.append("")

    # Add infoboxes if available and requested
    if include_infoboxes and response.infoboxes:
        output.append("=== INFOBOXES ===")
        for i, infobox in enumerate(response.infoboxes, 1):
            output.append(f"{i}. {infobox.infobox}")
            if infobox.id:
                output.append(f"   ID: {infobox.id}")
            output.append(f"   Content: {infobox.content}")
            if infobox.urls:
                output.append("   Related URLs:")
                for url in infobox.urls:
                    output.append(f"     - {url.title}: {url.url}")
            if infobox.img_src:
                output.append(f"   Image: {infobox.img_src}")
            output.append("")

    # Add direct answers if available
    if response.answers:
        output.append("=== DIRECT ANSWERS ===")
        for answer in response.answers:
            output.append(f"• {answer}")
        output.append("")

    # Add main search results
    if response.results:
        output.append("=== SEARCH RESULTS ===")
        for i, result in enumerate(response.results, 1):
            output.append(f"{i}. {result.title}")
            output.append(f"   URL: {result.url}")
            output.append(f"   Content: {result.content}")
            if result.engine:
                output.append(f"   Engine: {result.engine}")
            if result.publishedDate:
                output.append(f"   Published: {result.publishedDate}")
            output.append("")
    else:
        output.append("No search results found.")
        output.append("")

    # Add suggestions if available
    if response.suggestions:
        output.append("=== SUGGESTIONS ===")
        for suggestion in response.suggestions:
            output.append(f"• {suggestion}")
        output.append("")

    # Add corrections if available
    if response.corrections:
        output.append("=== CORRECTIONS ===")
        for correction in response.corrections:
            output.append(f"• {correction}")
        output.append("")

    return "\n".join(output)


async def test_searxng_connection() -> bool:
    """
    Test connection to SearXNG instance.

    Returns:
        True if connection is successful, False otherwise
    """
    try:
        async with httpx.AsyncClient(
            base_url=settings.searxng_url, timeout=10
        ) as client:
            response = await client.get("/")
            return response.status_code == 200
    except Exception as e:
        logger.error(f"Failed to connect to SearXNG at {settings.searxng_url}: {e}")
        return False
