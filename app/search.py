"""SearXNG search functionality with LangChain integration and Pydantic models."""

import logging
from typing import Any

from langchain_community.tools.searx_search.tool import SearxSearchResults
from langchain_community.utilities import SearxSearchWrapper
from pydantic import BaseModel, Field

from .config import settings

logger = logging.getLogger(__name__)


class SearchResult(BaseModel):
    """Individual search result from SearXNG."""

    url: str = Field(description="URL of the search result")
    title: str = Field(description="Title of the search result")
    content: str = Field(description="Content/snippet of the search result")
    engine: str | None = Field(
        default=None, description="Search engine that provided this result"
    )
    score: float | None = Field(default=None, description="Relevance score")
    category: str | None = Field(default=None, description="Category of the result")
    publishedDate: str | None = Field(
        default=None, description="Publication date if available"
    )


class InfoboxUrl(BaseModel):
    """URL within an infobox."""

    title: str = Field(description="Title of the URL")
    url: str = Field(description="The URL")


class Infobox(BaseModel):
    """Infobox result from SearXNG."""

    infobox: str = Field(description="Name/type of the infobox")
    id: str = Field(description="Unique identifier for the infobox")
    content: str = Field(description="Main content of the infobox")
    urls: list[InfoboxUrl] = Field(default_factory=list, description="Related URLs")
    img_src: str | None = Field(
        default=None, description="Image source URL if available"
    )
    engine: str | None = Field(
        default=None, description="Engine that provided the infobox"
    )


class SearXNGResponse(BaseModel):
    """Complete response from SearXNG API."""

    query: str = Field(description="The search query")
    number_of_results: int = Field(description="Total number of results found")
    results: list[SearchResult] = Field(
        default_factory=list, description="Search results"
    )
    infoboxes: list[Infobox] = Field(
        default_factory=list, description="Infobox results"
    )
    answers: list[str] = Field(default_factory=list, description="Direct answers")
    corrections: list[str] = Field(
        default_factory=list, description="Query corrections"
    )
    suggestions: list[str] = Field(
        default_factory=list, description="Search suggestions"
    )
    unresponsive_engines: list[str] = Field(
        default_factory=list, description="Engines that didn't respond"
    )


def _create_searx_tool(num_results: int = 10) -> SearxSearchResults:
    """Create and configure SearxSearchResults tool instance."""
    wrapper = SearxSearchWrapper(searx_host=settings.searxng_url)
    return SearxSearchResults(
        wrapper=wrapper,
        num_results=num_results
    )


async def search_searxng(
    query: str,
    limit: int | None = None,
    categories: str | None = None,
    engines: str | None = None,
    language: str | None = None,
    time_range: str | None = None,
    safesearch: int | None = None,
) -> SearXNGResponse:
    """
    Search using SearXNG via LangChain's SearxSearchResults tool.

    Args:
        query: Search query string
        limit: Maximum number of results to return (overrides settings default)
        categories: Comma-separated list of categories to search
        engines: Comma-separated list of engines to use
        language: Language code for search
        time_range: Time range filter (day, month, year)
        safesearch: Safe search level (0=off, 1=moderate, 2=strict)

    Returns:
        SearXNGResponse object with search results

    Raises:
        Exception: If the search fails
    """
    if limit is None:
        limit = settings.search_limit

    logger.info(f"Searching SearXNG for: {query}")

    try:
        # Create SearxSearchWrapper with parameters
        wrapper_params = {
            "searx_host": settings.searxng_url,
            "k": limit  # Number of results
        }

        # Add optional parameters to wrapper
        if categories:
            wrapper_params["categories"] = categories.split(",") if isinstance(categories, str) else categories
        if engines:
            wrapper_params["engines"] = engines.split(",") if isinstance(engines, str) else engines

        # Create wrapper
        wrapper = SearxSearchWrapper(**wrapper_params)

        # Create tool
        searx_tool = SearxSearchResults(
            wrapper=wrapper,
            num_results=limit
        )

        logger.debug(f"Search query: {query}")

        # Build additional search parameters for the run method
        search_kwargs = {}
        if language:
            search_kwargs["language"] = language
        if time_range:
            search_kwargs["time_range"] = time_range
        if safesearch is not None:
            search_kwargs["safesearch"] = safesearch

        # Perform search using LangChain's SearxSearchResults tool
        # This returns a JSON string with structured results
        if search_kwargs:
            results_json = searx_tool.run(query, **search_kwargs)
        else:
            results_json = searx_tool.run(query)

        logger.debug(f"Raw LangChain results: {results_json}")

        # Parse the JSON results and convert to our format
        searxng_response = _convert_langchain_results_to_searxng_response(
            query, results_json, limit
        )

        logger.info(
            f"Found {len(searxng_response.results)} results for query: {query}"
        )
        return searxng_response

    except Exception as e:
        logger.error(f"Error during SearXNG search: {e}")
        raise ValueError(f"Failed to search SearXNG: {e}") from e


def _convert_langchain_results_to_searxng_response(
    query: str, results_json: str, limit: int | None
) -> SearXNGResponse:
    """Convert LangChain SearxSearchResults JSON output to SearXNGResponse format."""
    import json

    try:
        # Parse the JSON results from LangChain
        # Handle empty or whitespace-only results
        if not results_json or results_json.strip() == "":
            logger.warning(f"Empty results returned from LangChain for query: {query}")
            return SearXNGResponse(
                query=query,
                number_of_results=0,
                results=[],
                infoboxes=[],
                answers=[],
                corrections=[],
                suggestions=[],
                unresponsive_engines=[]
            )

        # Parse JSON results
        raw_results = json.loads(results_json)

        # Handle case where raw_results is empty list or not a list
        if not isinstance(raw_results, list) or len(raw_results) == 0:
            logger.warning(f"No results found for query: {query}")
            return SearXNGResponse(
                query=query,
                number_of_results=0,
                results=[],
                infoboxes=[],
                answers=[],
                corrections=[],
                suggestions=[],
                unresponsive_engines=[]
            )

        # LangChain returns a list of result dictionaries
        search_results = []
        for result in raw_results:
            search_result = SearchResult(
                url=result.get('link', result.get('url', '')),
                title=result.get('title', ''),
                content=result.get('snippet', result.get('content', '')),
                engine=result.get('engine'),
                score=result.get('score'),
                category=result.get('category'),
                publishedDate=result.get('publishedDate')
            )
            search_results.append(search_result)

        # Apply limit if specified
        if limit and len(search_results) > limit:
            search_results = search_results[:limit]

        # Create response object
        response = SearXNGResponse(
            query=query,
            number_of_results=len(search_results),
            results=search_results,
            infoboxes=[],  # LangChain SearxSearchResults doesn't provide infoboxes
            answers=[],    # LangChain SearxSearchResults doesn't provide answers
            corrections=[], # LangChain SearxSearchResults doesn't provide corrections
            suggestions=[], # LangChain SearxSearchResults doesn't provide suggestions
            unresponsive_engines=[]  # LangChain SearxSearchResults doesn't provide this info
        )

        return response

    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse JSON results from LangChain: {e}")
        # Return empty response if JSON parsing fails
        return SearXNGResponse(
            query=query,
            number_of_results=0,
            results=[],
            infoboxes=[],
            answers=[],
            corrections=[],
            suggestions=[],
            unresponsive_engines=[]
        )


def format_search_results(
    response: SearXNGResponse, include_infoboxes: bool = True
) -> str:
    """
    Format search results into a readable text format.

    Args:
        response: SearXNG response object
        include_infoboxes: Whether to include infobox results

    Returns:
        Formatted string with search results
    """
    output = []

    # Add query information
    output.append(f"Search Query: {response.query}")
    output.append(f"Total Results: {response.number_of_results}")
    output.append("")

    # Add infoboxes if available and requested
    if include_infoboxes and response.infoboxes:
        output.append("=== INFOBOXES ===")
        for i, infobox in enumerate(response.infoboxes, 1):
            output.append(f"{i}. {infobox.infobox}")
            if infobox.id:
                output.append(f"   ID: {infobox.id}")
            output.append(f"   Content: {infobox.content}")
            if infobox.urls:
                output.append("   Related URLs:")
                for url in infobox.urls:
                    output.append(f"     - {url.title}: {url.url}")
            if infobox.img_src:
                output.append(f"   Image: {infobox.img_src}")
            output.append("")

    # Add direct answers if available
    if response.answers:
        output.append("=== DIRECT ANSWERS ===")
        for answer in response.answers:
            output.append(f"• {answer}")
        output.append("")

    # Add main search results
    if response.results:
        output.append("=== SEARCH RESULTS ===")
        for i, result in enumerate(response.results, 1):
            output.append(f"{i}. {result.title}")
            output.append(f"   URL: {result.url}")
            output.append(f"   Content: {result.content}")
            if result.engine:
                output.append(f"   Engine: {result.engine}")
            if result.publishedDate:
                output.append(f"   Published: {result.publishedDate}")
            output.append("")
    else:
        output.append("No search results found.")
        output.append("")

    # Add suggestions if available
    if response.suggestions:
        output.append("=== SUGGESTIONS ===")
        for suggestion in response.suggestions:
            output.append(f"• {suggestion}")
        output.append("")

    # Add corrections if available
    if response.corrections:
        output.append("=== CORRECTIONS ===")
        for correction in response.corrections:
            output.append(f"• {correction}")
        output.append("")

    return "\n".join(output)


async def test_searxng_connection() -> bool:
    """
    Test connection to SearXNG instance using LangChain wrapper.

    Returns:
        True if connection is successful, False otherwise
    """
    try:
        # Create a simple search tool and test with a basic query
        searx_tool = _create_searx_tool(num_results=1)

        # Try a simple search to test connectivity
        test_result = searx_tool.run({"query": "test"})

        # If we get any result without exception, connection is working
        return len(test_result.strip()) > 0

    except Exception as e:
        logger.error(f"Failed to connect to SearXNG at {settings.searxng_url}: {e}")
        return False
