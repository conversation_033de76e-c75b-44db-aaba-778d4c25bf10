"""SearXNG MCP resources - Read-only data sources that LLMs can access."""

import json
import logging
from datetime import datetime
from typing import Any

# Resources return simple strings, FastMCP handles the wrapping
from .config import settings

logger = logging.getLogger(__name__)


def register_resources(mcp: Any) -> None:
    """Register all SearXNG resources with the FastMCP server instance."""

    @mcp.resource("config://searxng")
    def get_searxng_config() -> str:
        """Get SearXNG configuration information."""
        config_info = {
            "server_name": settings.server_name,
            "server_version": settings.server_version,
            "searxng_url": settings.searxng_url,
            "search_limit": settings.search_limit,
            "search_timeout": settings.search_timeout,
            "transport": settings.transport,
            "debug": settings.debug,
            "log_level": settings.log_level,
        }
        return json.dumps(config_info, indent=2)

    @mcp.resource("help://search/{topic}")
    def get_search_help(topic: str) -> str:
        """Get help information for SearXNG search features.

        Args:
            topic: Help topic (syntax, categories, engines, filters, examples)
        """
        help_content = {
            "syntax": """
# SearXNG Search Syntax

## Basic Search
- Simple queries: `python programming`
- Exact phrases: `"machine learning"`
- Exclude terms: `python -snake`

## Advanced Operators
- Site search: `site:github.com python`
- File type: `filetype:pdf machine learning`
- Wildcard: `python * tutorial`

## Boolean Operators
- AND: `python AND tutorial` (default)
- OR: `python OR java`
- NOT: `python NOT snake`

## Special Searches
- Define: `define:algorithm`
- Weather: `weather:London`
- Currency: `100 USD to EUR`
            """.strip(),
            "categories": """
# SearXNG Search Categories

## Available Categories
- **general**: Web search (default)
- **news**: News articles and current events
- **images**: Image search
- **videos**: Video content
- **music**: Music and audio
- **files**: File downloads
- **science**: Scientific papers and research
- **map**: Maps and location data

## Usage Examples
- Search news: Use `categories=news`
- Search images: Use `categories=images`
- Multiple categories: Use `categories=general,news`
            """.strip(),
            "engines": """
# SearXNG Search Engines

## Popular Engines
- **Google**: Most comprehensive results
- **Bing**: Microsoft's search engine
- **DuckDuckGo**: Privacy-focused search
- **Brave**: Independent search
- **Startpage**: Google results with privacy
- **Searx**: Other SearXNG instances

## Specialized Engines
- **Wikipedia**: Encyclopedia articles
- **Reddit**: Community discussions
- **GitHub**: Code repositories
- **Stack Overflow**: Programming Q&A
- **arXiv**: Scientific papers

## Usage
- Single engine: Use `engines=google`
- Multiple engines: Use `engines=google,bing,duckduckgo`
            """.strip(),
            "filters": """
# SearXNG Search Filters

## Time Filters
- **day**: Results from past 24 hours
- **week**: Results from past week
- **month**: Results from past month
- **year**: Results from past year

## Safe Search Levels
- **0**: Off (no filtering)
- **1**: Moderate (default)
- **2**: Strict (maximum filtering)

## Language Filters
- **en**: English
- **es**: Spanish
- **fr**: French
- **de**: German
- **auto**: Auto-detect

## Result Limits
- Default: 10 results
- Maximum: 50 results per search
            """.strip(),
            "examples": """
# SearXNG Search Examples

## Basic Searches
```
search("python programming tutorial")
search("climate change 2024", categories="news")
search("machine learning", engines="google,bing")
```

## Advanced Searches
```
search("site:github.com python", limit=20)
search("artificial intelligence", time_range="month")
search("data science", safesearch=0, language="en")
```

## Research Searches
```
search("quantum computing", categories="science")
search("covid vaccine", categories="news", time_range="week")
search("python pandas tutorial", categories="general,videos")
```

## Comparison Searches
```
search("iPhone vs Android 2024")
search("Tesla vs BMW electric cars")
search("Python vs JavaScript performance")
```
            """.strip(),
        }

        if topic not in help_content:
            available_topics = list(help_content.keys())
            return f"Unknown help topic: {topic}\nAvailable topics: {', '.join(available_topics)}"

        return help_content[topic]

    @mcp.resource("status://searxng")
    def get_searxng_status() -> str:
        """Get SearXNG server status and health information."""
        status_data = {
            "server_status": "running",
            "searxng_url": settings.searxng_url,
            "timestamp": datetime.now().isoformat(),
            "version": settings.server_version,
            "transport": settings.transport,
            "debug_mode": settings.debug,
            "search_timeout": settings.search_timeout,
            "max_results": settings.search_limit,
        }
        return json.dumps(status_data, indent=2)

    @mcp.resource("docs://searxng/{section}")
    def get_searxng_documentation(section: str) -> str:
        """Get SearXNG MCP server documentation.

        Args:
            section: Documentation section (overview, tools, resources, prompts, setup)
        """
        docs = {
            "overview": """
# SearXNG MCP Server

This server provides access to SearXNG metasearch capabilities through MCP:
- Web search aggregating multiple search engines
- News search with time filtering
- Academic and research search capabilities
- Image and video search
- Fact-checking and comparison tools
- Connection testing and health monitoring

## Features
- Aggregates results from 249+ search engines
- Privacy-focused (no tracking)
- Multiple search categories and filters
- Configurable result limits and timeouts
- Safe search filtering
- Multi-language support

## Available Transports
- STDIO: For local command-line usage
- HTTP: For web-based deployments (recommended)
- SSE: Legacy transport support
            """.strip(),
            "tools": """
# Available SearXNG Tools

## Core Search Tools
- `search(query, ...)`: Main search tool with full parameter control
- `quick_search(query)`: Fast search with default settings (5 results)
- `search_with_filters(query, category, engine, safe_search)`: Predefined filter combinations

## Utility Tools
- `test_connection()`: Test connection to SearXNG instance

## Search Parameters
- **query**: Search query string (required)
- **limit**: Max results (1-50, default from config)
- **categories**: Comma-separated categories (general, news, images, etc.)
- **engines**: Comma-separated engine names
- **language**: Language code (en, es, fr, etc.)
- **time_range**: Time filter (day, week, month, year)
- **safesearch**: Safe search level (0=off, 1=moderate, 2=strict)
- **include_infoboxes**: Include infobox results (default: true)
            """.strip(),
            "resources": """
# Available SearXNG Resources

## Configuration
- `config://searxng`: SearXNG server configuration

## Help Documentation
- `help://search/syntax`: Search syntax and operators
- `help://search/categories`: Available search categories
- `help://search/engines`: Supported search engines
- `help://search/filters`: Time, language, and safety filters
- `help://search/examples`: Example search queries

## Status
- `status://searxng`: SearXNG server status and health

## Documentation
- `docs://searxng/{section}`: This documentation system
            """.strip(),
            "prompts": """
# Available SearXNG Prompts

## Search Prompts
- `search_prompt(query, context)`: Basic search prompt generation
- `research_prompt(topic, depth)`: Research-focused search prompts
- `news_search_prompt(topic, time_range)`: News search prompts
- `fact_check_prompt(claim, sources_required)`: Fact-checking prompts
- `comparison_search_prompt(items, criteria)`: Comparison research prompts
- `academic_search_prompt(topic, level)`: Academic research prompts

## Prompt Parameters
- **query/topic**: Main search subject
- **context**: Additional context for search
- **depth**: Research depth (quick, comprehensive, academic)
- **time_range**: Time focus (recent, day, week, month)
- **sources_required**: Minimum sources for fact-checking
- **criteria**: Specific comparison criteria
- **level**: Academic level (undergraduate, graduate, research)
            """.strip(),
            "setup": """
# SearXNG MCP Server Setup

## Prerequisites
- Python 3.13+
- Access to a SearXNG instance (local or remote)

## Installation
```bash
# Install the package
pip install mcp-searxng

# Or install from source
git clone <repository>
cd mcp-searxng
pip install -e .
```

## Configuration
Set environment variables:
```bash
export SEARXNG_URL="http://localhost:8080"      # Your SearXNG instance
export MCP_SEARCH_LIMIT=10                      # Default result limit
export MCP_SEARCH_TIMEOUT=30                    # Request timeout
```

## Running the Server
```bash
# Development mode
mcp-searxng-dev

# Production mode
mcp-searxng-prod

# Custom configuration
MCP_TRANSPORT=http MCP_PORT=9000 mcp-searxng
```

## SearXNG Instance
You need access to a SearXNG instance. Options:
1. Run locally: https://docs.searxng.org/admin/installation.html
2. Use public instance: https://searx.space
3. Docker: `docker run -p 8080:8080 searxng/searxng`
            """.strip(),
        }

        if section not in docs:
            available_sections = list(docs.keys())
            return f"Unknown documentation section: {section}\nAvailable sections: {', '.join(available_sections)}"

        return docs[section]

    @mcp.resource("examples://search/{use_case}")
    def get_search_examples(use_case: str) -> str:
        """Get search examples for different use cases.

        Args:
            use_case: Use case type (basic, research, news, academic, comparison)
        """
        examples = {
            "basic": """
# Basic SearXNG Search Examples

## Simple Searches
search("python programming tutorial")
search("climate change effects")
search("best restaurants near me")

## Quick Searches (5 results max)
quick_search("latest AI developments")
quick_search("weather forecast")

## Filtered Searches
search_with_filters("machine learning", category="general", safe_search=True)
search_with_filters("breaking news", category="news", safe_search=False)
            """.strip(),
            "research": """
# Research Search Examples

## Academic Research
search("quantum computing applications", categories="science", limit=20)
search("climate change research 2024", engines="google,bing", time_range="year")

## Comprehensive Research
search("artificial intelligence ethics",
       categories="general,news,science",
       language="en",
       limit=30)

## Source Verification
search("covid vaccine effectiveness",
       engines="google,bing,duckduckgo",
       safesearch=1,
       time_range="month")
            """.strip(),
            "news": """
# News Search Examples

## Current Events
search("latest technology news", categories="news", time_range="day")
search("election results 2024", categories="news", engines="google,bing")

## Breaking News
search("breaking news today", categories="news", limit=15)
search("stock market crash", categories="news", time_range="week")

## International News
search("ukraine war updates", categories="news", language="en", time_range="day")
search("climate summit 2024", categories="news", engines="google,bing,duckduckgo")
            """.strip(),
            "academic": """
# Academic Search Examples

## Scientific Papers
search("machine learning algorithms", categories="science", engines="google")
search("climate change research", categories="science", limit=25)

## Educational Content
search("quantum physics tutorial", categories="general", engines="wikipedia")
search("organic chemistry basics", categories="general,science")

## Research Papers
search("artificial neural networks",
       categories="science",
       engines="google,bing",
       language="en",
       limit=20)
            """.strip(),
            "comparison": """
# Comparison Search Examples

## Product Comparisons
search("iPhone 15 vs Samsung Galaxy S24")
search("Tesla Model 3 vs BMW i4 comparison")
search("MacBook Pro vs ThinkPad X1 Carbon")

## Service Comparisons
search("Netflix vs Disney Plus vs Hulu")
search("AWS vs Azure vs Google Cloud")
search("Spotify vs Apple Music vs YouTube Music")

## Technology Comparisons
search("Python vs JavaScript performance")
search("React vs Vue vs Angular 2024")
search("PostgreSQL vs MySQL vs MongoDB")
            """.strip(),
        }

        if use_case not in examples:
            available_cases = list(examples.keys())
            return f"No examples available for {use_case}. Available: {', '.join(available_cases)}"

        return examples[use_case]
