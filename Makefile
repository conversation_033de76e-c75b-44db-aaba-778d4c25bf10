.PHONY: help clean install test lint typecheck dev server client up-dev down-dev up-prod down-prod

# Default target
.DEFAULT_GOAL := help

# Colors for output
BLUE := \033[0;34m
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
NC := \033[0m # No Color

help: ## Show this help message
	@echo "FastMCP Template - Available commands:"
	@echo ""
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

add-shared-net: ## Add shared network (idempotent)
	@echo "$(BLUE)Adding shared network...$(NC)"
	@docker network create shared-net || \
		echo "$(GREEN)Shared network already exists$(NC)"

# Development
install: ## Install dependencies
	uv sync --dev

dev: ## Run development server with auto-reload
	uv run mcp-dev

server: ## Run server (default STDIO transport)
	uv run mcp-server

client: ## Run example client
	uv run python examples/client_example.py

# Testing and Quality
test: ## Run tests
	uv run pytest

test-cov: ## Run tests with coverage
	uv run pytest --cov=app --cov-report=html --cov-report=term

lint: ## Run linting and formatting
	uv run ruff check .
	uv run ruff format .

lint-check: ## Check linting without fixing
	uv run ruff check .
	uv run ruff format --check .

typecheck: ## Run type checking
	uv run mypy app/

# Cleanup
clean: ## Clean up cache and temporary files
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	rm -rf .pytest_cache
	rm -rf .coverage
	rm -rf htmlcov
	rm -rf dist
	rm -rf build
	rm -rf *.egg-info
	rm -rf temp_client.py

# Docker Compose environments
up-dev: add-shared-net ## Start development environment with Docker Compose
	docker compose up --build -d

logs-dev: ## Logs development environment with Docker Compose
	docker compose logs -f

down-dev: ## Stop development environment
	docker compose down

up-prod: add-shared-net ## Start production environment with Docker Compose
	docker compose -f docker-compose.prod.yml up --build -d

logs-prod: ## Logs production environment with Docker Compose
	docker compose -f docker-compose.prod.yml logs -f

down-prod: ## Stop production environment
	docker compose -f docker-compose.prod.yml down
