# mcp-searxng

A FastMCP server for connecting agentic systems to search systems via [SearXNG](https://docs.searxng.org/). This server provides comprehensive web search capabilities through SearXNG's metasearch engine, which aggregates results from 249+ search engines including Google, Bing, DuckDuckGo, Brave, and many others. Built with [LangChain Community](https://python.langchain.com/docs/integrations/providers/searx/) integration for reliable and well-tested search functionality.

## Features

- **🔍 Comprehensive Search**: Access to 249+ search engines through SearXNG
- **🛡️ Privacy-Focused**: No user tracking or profiling (SearXNG principle)
- **🚀 FastMCP Integration**: Built with the latest FastMCP framework
- **🦜 LangChain Integration**: Uses LangChain Community's SearxSearchResults for reliable search functionality
- **🌐 Multiple Categories**: Search general web, news, images, videos, academic content, and more
- **⚙️ Flexible Filtering**: Time range, language, safe search, and engine-specific filtering
- **📊 Structured Results**: Returns JSON-structured search results with metadata
- **🧪 Testing Framework**: Comprehensive tests using in-memory transport
- **🔧 Development Tools**: Hot reloading, linting, and type checking

## Quick Start

### Prerequisites

- Python 3.13+
- [uv](https://docs.astral.sh/uv/) (recommended) or pip
- Access to a SearXNG instance (local or remote)
- Docker (optional, for containerized deployment)

### Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd mcp-searxng
   ```

2. **Install dependencies:**
   ```bash
   # Using uv (recommended)
   uv sync

   # Or using pip
   pip install -e .
   ```

3. **Set up SearXNG instance:**
   ```bash
   # Option 1: Use a public instance (see https://searx.space)
   export SEARXNG_URL="https://searx.example.com"

   # Option 2: Run locally with Docker
   docker run -p 8080:8080 searxng/searxng
   export SEARXNG_URL="http://localhost:8080"
   ```

### Running the Server

#### Local Development (STDIO)
```bash
# Default STDIO transport for local usage
uv run mcp-searxng

# Or directly with Python
python -m app.main
```

#### Web Server (HTTP Streamable)
```bash
# Development mode with auto-reload
uv run mcp-searxng-dev

# Production mode
uv run mcp-searxng-prod

# Custom configuration
MCP_TRANSPORT=http MCP_PORT=9000 uv run mcp-searxng
```

#### Using Docker
```bash
# Development environment
make up-dev

# View logs
make logs-dev

# Stop
make down-dev
```

## Architecture

### Project Structure
```
mcp-searxng/
├── app/
│   ├── __init__.py
│   ├── main.py          # Main server setup and entry points
│   ├── config.py        # Pydantic settings configuration
│   ├── search.py        # SearXNG search functionality and models
│   ├── tools.py         # MCP tools (search functions LLMs can call)
│   ├── resources.py     # MCP resources (help, config, examples)
│   └── prompts.py       # MCP prompts (search-related templates)
├── tests/
│   ├── __init__.py
│   └── test_mcp_server.py  # Comprehensive test suite
├── docker-compose.yml   # Development Docker setup
├── Dockerfile.dev       # Development Docker image
├── pyproject.toml       # Project configuration
└── README.md
```

### Core Components

#### Search Module (`app/search.py`)
Core SearXNG integration with Pydantic models:
- **SearXNG API Client**: Async HTTP client for SearXNG instances
- **Response Models**: Structured models for search results, infoboxes, and metadata
- **Result Formatting**: Convert API responses to readable text format
- **Connection Testing**: Verify SearXNG instance availability

#### Tools (`app/tools.py`)
Search functions that LLMs can call:
- **Main Search**: `search()` - Full-featured search with all parameters
- **Quick Search**: `quick_search()` - Fast search with default settings
- **Filtered Search**: `search_with_filters()` - Predefined filter combinations
- **Connection Test**: `test_connection()` - Verify SearXNG connectivity

#### Resources (`app/resources.py`)
Read-only data sources for search assistance:
- **Configuration**: `config://searxng` - SearXNG server configuration
- **Search Help**: `help://search/{topic}` - Syntax, categories, engines, filters, examples
- **Status**: `status://searxng` - Server health and status information
- **Documentation**: `docs://searxng/{section}` - Complete API documentation
- **Examples**: `examples://search/{use_case}` - Search examples for different scenarios

#### Prompts (`app/prompts.py`)
Search-focused message templates:
- **Basic Search**: `search_prompt()` - Generate search prompts with context
- **Research**: `research_prompt()` - Academic and comprehensive research prompts
- **News Search**: `news_search_prompt()` - Time-sensitive news search prompts
- **Fact Checking**: `fact_check_prompt()` - Multi-source verification prompts
- **Comparison**: `comparison_search_prompt()` - Comparative analysis prompts
- **Academic**: `academic_search_prompt()` - Scholarly research prompts

## Configuration

The server uses Pydantic Settings for configuration management. All settings can be configured via environment variables with the `MCP_` prefix.

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `MCP_SERVER_NAME` | "SearXNG MCP Server" | Server name |
| `MCP_SERVER_VERSION` | "0.1.0" | Server version |
| `SEARXNG_URL` | "http://localhost:8080" | SearXNG instance URL |
| `MCP_SEARCH_LIMIT` | 10 | Maximum search results (1-50) |
| `MCP_SEARCH_TIMEOUT` | 30 | Search timeout in seconds (5-120) |
| `MCP_TRANSPORT` | "stdio" | Transport protocol (stdio, http, sse) |
| `MCP_HOST` | "127.0.0.1" | Server host (for HTTP/SSE) |
| `MCP_PORT` | 8000 | Server port (for HTTP/SSE) |
| `MCP_PATH` | "/mcp" | MCP endpoint path (for HTTP) |
| `MCP_DEBUG` | false | Debug mode |
| `MCP_LOG_LEVEL` | "info" | Logging level |
| `MCP_AUTH_ENABLED` | false | Enable authentication |
| `MCP_AUTH_SECRET` | "dev-secret-change-in-production" | Auth secret |

### Transport Options

#### STDIO (Default)
Best for local tools and command-line integrations:
```bash
MCP_TRANSPORT=stdio uv run mcp-server
```

#### HTTP Streamable (Recommended for Web)
Modern, efficient transport for web deployments:
```bash
MCP_TRANSPORT=http MCP_HOST=0.0.0.0 MCP_PORT=8000 uv run mcp-server
```

#### SSE (Legacy)
Server-Sent Events transport (deprecated, use HTTP instead):
```bash
MCP_TRANSPORT=sse MCP_HOST=0.0.0.0 MCP_PORT=8000 uv run mcp-server
```

### SearXNG Setup

You need access to a SearXNG instance to use this server. Here are your options:

#### Option 1: Use a Public Instance
Visit [searx.space](https://searx.space) to find public SearXNG instances:
```bash
export SEARXNG_URL="https://searx.example.com"
```

#### Option 2: Run SearXNG Locally with Docker
```bash
# Run SearXNG in Docker
docker run -d -p 8080:8080 searxng/searxng

# Set the URL
export SEARXNG_URL="http://localhost:8080"
```

#### Option 3: Install SearXNG from Source
Follow the [official installation guide](https://docs.searxng.org/admin/installation.html):
```bash
# Example for local installation
git clone https://github.com/searxng/searxng.git
cd searxng
# Follow installation instructions...
```

## Usage Examples

### Client Connection

#### Python Client (In-Memory)
```python
from fastmcp import Client
from app.main import mcp

async def main():
    async with Client(mcp) as client:
        # Call a tool
        result = await client.call_tool("add_numbers", {"a": 5, "b": 3})
        print(f"Result: {result.text}")

        # Read a resource
        config = await client.read_resource("config://server")
        print(f"Config: {config.text}")

        # Get a prompt
        prompt = await client.get_prompt("analyze_data_prompt", {
            "data_description": "Sales data Q1 2024",
            "analysis_type": "trends"
        })
        print(f"Prompt: {prompt.messages[0].content.text}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
```

#### Python Client (HTTP)
```python
from fastmcp import Client

async def main():
    async with Client("http://localhost:8000/mcp") as client:
        # Same usage as above
        result = await client.call_tool("multiply_numbers", {"a": 4, "b": 7})
        print(f"Result: {result.text}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
```

#### Claude Desktop Integration
Add to your Claude Desktop configuration:
```json
{
  "mcpServers": {
    "fastmcp-template": {
      "command": "uv",
      "args": ["run", "mcp-server"],
      "cwd": "/path/to/app-template-fastmcp"
    }
  }
}
```

## Testing

The project includes comprehensive tests using FastMCP's in-memory transport for efficient testing without subprocess or network overhead.

### Running Tests
```bash
# Run all tests
uv run pytest

# Run with coverage
uv run pytest --cov=app --cov-report=html

# Run specific test file
uv run pytest tests/test_mcp_server.py

# Run with verbose output
uv run pytest -v
```

### Test Coverage
The test suite covers:
- ✅ Server capabilities and info
- ✅ Tool listing and execution
- ✅ Resource listing and reading
- ✅ Prompt listing and generation
- ✅ Error handling
- ✅ All transport types

## Development

### Development Setup
```bash
# Install development dependencies
uv sync --dev

# Install pre-commit hooks
uv run pre-commit install

# Run linting and formatting
uv run ruff check .
uv run ruff format .

# Run type checking
uv run mypy app/
```

### Adding New Components

#### Adding a New Tool
1. Add your tool function to `app/tools.py`:
```python
@mcp.tool
def my_new_tool(param: str) -> str:
    """Description of what the tool does."""
    return f"Processed: {param}"
```

2. The tool will be automatically registered when the server starts.

#### Adding a New Resource
1. Add your resource function to `app/resources.py`:
```python
@mcp.resource("mydata://info/{id}")
def get_my_data(id: str) -> JsonResource:
    """Get my custom data."""
    return JsonResource(data={"id": id, "data": "example"})
```

#### Adding a New Prompt
1. Add your prompt function to `app/prompts.py`:
```python
@mcp.prompt
def my_custom_prompt(topic: str) -> str:
    """Generate a custom prompt."""
    return f"Please analyze the following topic: {topic}"
```

### Docker Development

#### Development Environment
```bash
# Start development environment
make up-dev

# View logs
make logs-dev

# Stop environment
make down-dev

# Clean up
make clean
```

#### Production Deployment
```bash
# Start production environment
make up-prod

# View logs
make logs-prod

# Stop environment
make down-prod
```

## Deployment

### Local Deployment
```bash
# Install and run
uv sync
MCP_TRANSPORT=http MCP_HOST=0.0.0.0 uv run mcp-prod
```

### Docker Deployment
```bash
# Build and run production container
docker build -f Dockerfile.prod -t fastmcp-template .
docker run -p 8000:8000 -e MCP_TRANSPORT=http fastmcp-template
```

### Cloud Deployment
The server can be deployed to any cloud platform that supports Python applications:

- **Railway**: Deploy directly from GitHub
- **Heroku**: Use the included Dockerfile
- **Google Cloud Run**: Deploy as a container
- **AWS Lambda**: Use with serverless frameworks
- **DigitalOcean App Platform**: Deploy from GitHub

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and add tests
4. Run the test suite: `uv run pytest`
5. Run linting: `uv run ruff check .`
6. Commit your changes: `git commit -m 'Add amazing feature'`
7. Push to the branch: `git push origin feature/amazing-feature`
8. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Resources

- [FastMCP Documentation](https://gofastmcp.com)
- [Model Context Protocol](https://modelcontextprotocol.io)
- [FastMCP GitHub Repository](https://github.com/jlowin/fastmcp)
- [MCP Specification](https://spec.modelcontextprotocol.io)

## Support

If you encounter any issues or have questions:

1. Check the [FastMCP Documentation](https://gofastmcp.com)
2. Search existing [GitHub Issues](https://github.com/jlowin/fastmcp/issues)
3. Create a new issue with detailed information about your problem

---

**Happy building with FastMCP! 🚀**
