# SearXNG MCP Server Configuration
# Copy this file to .env and customize the values

# Server Information
MCP_SERVER_NAME="SearXNG MCP Server"
MCP_SERVER_VERSION="0.1.0"
MCP_DEBUG=false

# SearXNG Configuration
SEARXNG_URL="http://searxng:8080"
MCP_SEARCH_LIMIT=10
MCP_SEARCH_TIMEOUT=30

# Transport Configuration
# Options: stdio, http, sse
MCP_TRANSPORT=http
MCP_HOST=0.0.0.0
MCP_PORT=8000
MCP_PATH=/mcp

# Logging
# Options: debug, info, warning, error, critical
MCP_LOG_LEVEL=info

# Authentication (for HTTP/SSE transports)
MCP_AUTH_ENABLED=false
MCP_AUTH_SECRET=dev-secret-key-for-development-only-change-in-production

# SearXNG Instance Configuration
# Set this to your SearXNG instance URL
# Examples:
# - Local Docker: http://localhost:8080
# - Public instance: https://searx.example.com
SEARXNG_URL="http://localhost:8080"
