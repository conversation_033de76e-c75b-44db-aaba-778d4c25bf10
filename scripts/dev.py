#!/usr/bin/env python3
"""Development script for FastMCP template."""

import argparse
import subprocess
import sys
from pathlib import Path


def run_command(cmd: list[str], cwd: Path | None = None) -> int:
    """Run a command and return the exit code."""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, cwd=cwd)
    return result.returncode


def main() -> int:
    """Main development script."""
    parser = argparse.ArgumentParser(description="FastMCP development tools")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Server commands
    server_parser = subparsers.add_parser("server", help="Run the MCP server")
    server_parser.add_argument(
        "--transport",
        choices=["stdio", "http", "sse"],
        default="stdio",
        help="Transport protocol",
    )
    server_parser.add_argument(
        "--port", type=int, default=8000, help="Port for HTTP/SSE"
    )
    server_parser.add_argument("--host", default="127.0.0.1", help="Host for HTTP/SSE")
    server_parser.add_argument("--debug", action="store_true", help="Enable debug mode")

    # Test commands
    test_parser = subparsers.add_parser("test", help="Run tests")
    test_parser.add_argument(
        "--coverage", action="store_true", help="Run with coverage"
    )
    test_parser.add_argument(
        "--verbose", "-v", action="store_true", help="Verbose output"
    )
    test_parser.add_argument("pattern", nargs="?", help="Test pattern to match")

    # Lint commands
    lint_parser = subparsers.add_parser("lint", help="Run linting and formatting")
    lint_parser.add_argument("--fix", action="store_true", help="Auto-fix issues")
    lint_parser.add_argument(
        "--check-only", action="store_true", help="Check only, don't format"
    )

    # Type check commands
    subparsers.add_parser("typecheck", help="Run type checking")

    # Install commands
    install_parser = subparsers.add_parser("install", help="Install dependencies")
    install_parser.add_argument(
        "--dev", action="store_true", help="Install dev dependencies"
    )

    # Client commands
    client_parser = subparsers.add_parser("client", help="Run test client")
    client_parser.add_argument("--url", help="Server URL (for HTTP transport)")
    client_parser.add_argument(
        "--transport",
        choices=["stdio", "http", "memory"],
        default="memory",
        help="Client transport",
    )

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return 1

    project_root = Path(__file__).parent.parent

    if args.command == "server":
        env = {}
        if args.debug:
            env["MCP_DEBUG"] = "true"
            env["MCP_LOG_LEVEL"] = "debug"

        env["MCP_TRANSPORT"] = args.transport
        if args.transport in ("http", "sse"):
            env["MCP_HOST"] = args.host
            env["MCP_PORT"] = str(args.port)

        # Set environment variables and run server
        import os

        os.environ.update(env)

        if args.transport == "stdio":
            cmd = ["uv", "run", "mcp-server"]
        else:
            cmd = ["uv", "run", "mcp-dev" if args.debug else "mcp-prod"]

        return run_command(cmd, cwd=project_root)

    elif args.command == "test":
        cmd = ["uv", "run", "pytest"]

        if args.coverage:
            cmd.extend(["--cov=app", "--cov-report=html", "--cov-report=term"])

        if args.verbose:
            cmd.append("-v")

        if args.pattern:
            cmd.extend(["-k", args.pattern])

        return run_command(cmd, cwd=project_root)

    elif args.command == "lint":
        if args.check_only:
            # Just check, don't format
            cmd1 = ["uv", "run", "ruff", "check", "."]
            cmd2 = ["uv", "run", "ruff", "format", "--check", "."]

            exit_code = run_command(cmd1, cwd=project_root)
            if exit_code != 0:
                return exit_code
            return run_command(cmd2, cwd=project_root)

        elif args.fix:
            # Auto-fix and format
            cmd1 = ["uv", "run", "ruff", "check", "--fix", "."]
            cmd2 = ["uv", "run", "ruff", "format", "."]

            exit_code = run_command(cmd1, cwd=project_root)
            if exit_code != 0:
                return exit_code
            return run_command(cmd2, cwd=project_root)

        else:
            # Check and format
            cmd1 = ["uv", "run", "ruff", "check", "."]
            cmd2 = ["uv", "run", "ruff", "format", "."]

            exit_code = run_command(cmd1, cwd=project_root)
            run_command(cmd2, cwd=project_root)  # Format regardless of check result
            return exit_code

    elif args.command == "typecheck":
        cmd = ["uv", "run", "mypy", "app/"]
        return run_command(cmd, cwd=project_root)

    elif args.command == "install":
        if args.dev:
            cmd = ["uv", "sync", "--dev"]
        else:
            cmd = ["uv", "sync"]

        return run_command(cmd, cwd=project_root)

    elif args.command == "client":
        # Create a simple test client
        client_script = f'''
import asyncio
from fastmcp import Client

async def main():
    if "{args.transport}" == "memory":
        from app.main import mcp
        client_target = mcp
    elif "{args.transport}" == "http":
        client_target = "{args.url or "http://localhost:8000/mcp"}"
    else:  # stdio
        client_target = "app/main.py"

    async with Client(client_target) as client:
        print("Connected to MCP server!")

        # List tools
        tools = await client.list_tools()
        print(f"Available tools: {{[tool.name for tool in tools.tools]}}")

        # Test a simple tool
        result = await client.call_tool("add_numbers", {{"a": 5, "b": 3}})
        print(f"add_numbers(5, 3) = {{result.text}}")

        # List resources
        resources = await client.list_resources()
        print(f"Available resources: {{[r.uri_template for r in resources.resources]}}")

        # Read a resource
        config = await client.read_resource("config://server")
        print(f"Server config: {{config.text[:100]}}...")

if __name__ == "__main__":
    asyncio.run(main())
'''

        # Write and run the client script
        client_file = project_root / "temp_client.py"
        client_file.write_text(client_script)

        try:
            cmd = ["uv", "run", "python", str(client_file)]
            return run_command(cmd, cwd=project_root)
        finally:
            if client_file.exists():
                client_file.unlink()

    return 0


if __name__ == "__main__":
    sys.exit(main())
