#!/usr/bin/env python3
"""Example FastMCP client demonstrating different connection methods."""

import asyncio
import json
from pathlib import Path

from fastmcp import Client


async def test_in_memory_client() -> None:
    """Test client using in-memory transport (for testing)."""
    print("=== Testing In-Memory Client ===")

    # Import the server instance
    from app.main import mcp

    async with Client(mcp) as client:
        print("✅ Connected to MCP server (in-memory)")

        # Test connection with ping
        await client.ping()
        print("Server connection verified")

        # List and call tools
        tools = await client.list_tools()
        print(f"Available tools: {[tool.name for tool in tools]}")

        # Test mathematical tools
        result = await client.call_tool("add_numbers", {"a": 10, "b": 5})
        print(f"add_numbers(10, 5) = {result}")

        result = await client.call_tool("multiply_numbers", {"a": 4, "b": 7})
        print(f"multiply_numbers(4, 7) = {result}")

        # Test text formatting
        result = await client.call_tool(
            "format_text", {"text": "hello world", "format_type": "title"}
        )
        print(f"format_text('hello world', 'title') = {result}")

        # Read resources
        config = await client.read_resource("config://server")
        config_data = json.loads(config[0].text)
        print(f"Server config: {config_data['name']} ({config_data['transport']})")

        # Read sample data (this is a resource template)
        try:
            users = await client.read_resource("data://sample/users")
            users_data = json.loads(users[0].text)
            print(f"Sample users: {len(users_data)} users loaded")
        except Exception as e:
            print(f"Could not read sample users (resource template): {e}")

        # Test prompts
        prompt = await client.get_prompt(
            "analyze_data_prompt",
            {"data_description": "Q1 2024 sales data", "analysis_type": "trends"},
        )
        print(f"Generated prompt: {str(prompt)[:100]}...")


async def test_stdio_client() -> None:
    """Test client using STDIO transport (subprocess)."""
    print("\n=== Testing STDIO Client ===")

    try:
        async with Client(Path("app/main.py")) as client:
            print("✅ Connected to MCP server (STDIO)")

            # Quick test
            result = await client.call_tool("add_numbers", {"a": 2, "b": 3})
            print(f"add_numbers(2, 3) = {result}")

            # Read health status
            health = await client.read_resource("status://health")
            health_data = json.loads(health[0].text)
            print(f"Server status: {health_data['status']}")

    except Exception as e:
        print(f"❌ STDIO client failed: {e}")


async def test_http_client() -> None:
    """Test client using HTTP transport (requires running server)."""
    print("\n=== Testing HTTP Client ===")

    try:
        async with Client("http://localhost:8000/mcp") as client:
            print("✅ Connected to MCP server (HTTP)")

            # Quick test
            result = await client.call_tool("get_current_time", {"timezone": "UTC"})
            print(f"Current time: {result}")

            # Read documentation (resource template)
            try:
                docs = await client.read_resource("docs://api/overview")
                print(f"Documentation preview: {docs[0].text[:100]}...")
            except Exception as e:
                print(f"Could not read documentation (resource template): {e}")

    except Exception as e:
        print(f"❌ HTTP client failed: {e}")
        print("💡 Make sure the server is running with: uv run mcp-dev")


async def test_multi_server_client() -> None:
    """Test client connecting to multiple servers via MCP config."""
    print("\n=== Testing Multi-Server Client ===")

    # Example MCP configuration
    config = {
        "mcpServers": {
            "template": {
                "command": "python",
                "args": ["-m", "app.main"],
                "env": {"MCP_TRANSPORT": "stdio"},
            }
        }
    }

    try:
        async with Client(config) as client:
            print("✅ Connected to multiple MCP servers")

            # Tools are prefixed with server name
            result = await client.call_tool("template_add_numbers", {"a": 1, "b": 1})
            print(f"template_add_numbers(1, 1) = {result}")

    except Exception as e:
        print(f"❌ Multi-server client failed: {e}")


async def main() -> None:
    """Run all client examples."""
    print("FastMCP Client Examples")
    print("=" * 50)

    # Test different client types
    await test_in_memory_client()
    await test_stdio_client()
    await test_http_client()
    await test_multi_server_client()

    print("\n" + "=" * 50)
    print("Client examples completed!")
    print("\nTo run the HTTP example successfully:")
    print("1. Start the server: uv run mcp-dev")
    print("2. Run this script again: python examples/client_example.py")


if __name__ == "__main__":
    asyncio.run(main())
