# Git
.git
.gitignore

# Python
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.pytest_cache
nosetests.xml
coverage.xml
*.cover
*.log
.mypy_cache
.dmypy.json
dmypy.json

# Virtual environments
.venv
venv/
ENV/
env/
.env

# IDEs
.vscode
.idea
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
tests/
docs/
*.md
!README.md
Makefile
.pre-commit-config.yaml

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Development files
htmlcov/
.pytest_cache/
dist/
build/
*.egg-info/
