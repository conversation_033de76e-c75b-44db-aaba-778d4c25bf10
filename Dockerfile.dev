# Development Dockerfile for FastMCP server
FROM python:3.13-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    UV_LINK_MODE=copy

# Install uv
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /usr/local/bin/

# Create app directory
WORKDIR /app

# Copy dependency files
COPY pyproject.toml ./

# Install dependencies with uv
RUN uv sync --dev --no-install-project

# Copy application code
COPY . .

# Install the project in development mode
RUN uv pip install -e .

# Expose port
EXPOSE 8000

# Command for development with hot reloading
CMD ["uv", "run", "mcp-searxng-dev"]
