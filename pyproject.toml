[project]
name = "mcp-searxng"
version = "0.1.0"
description = "FastMCP server for connecting agentic systems to search systems via SearXNG"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "fastmcp>=2.10.6",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "uvicorn>=0.24.0",
    "httpx>=0.25.0",
]
authors = [
    {name = "SearXNG MCP Team", email = "<EMAIL>"},
]
keywords = ["fastmcp", "mcp", "searxng", "search", "metasearch", "llm", "ai"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.13",

    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.25.0",
    "pre-commit>=3.5.0",
    "ruff>=0.1.0",
    "mypy>=1.7.0",
    "coverage>=7.3.0",
]

[project.scripts]
mcp-searxng = "app.main:main"
mcp-searxng-dev = "app.main:run_dev"
mcp-searxng-prod = "app.main:run_prod"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["app"]

[tool.ruff]
target-version = "py310"
line-length = 88

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]

[tool.mypy]
python_version = "3.10"
check_untyped_defs = true
disallow_any_generics = true
disallow_incomplete_defs = true
disallow_untyped_defs = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"

[tool.coverage.run]
source = ["app"]
omit = ["*/tests/*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
]

[dependency-groups]
dev = [
    "mypy>=1.17.0",
    "pytest>=8.4.1",
    "pytest-asyncio>=1.1.0",
]
